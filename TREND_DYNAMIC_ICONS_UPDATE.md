# Trend 页面动态图标配置更新

## 🎯 更新目标

实现基于流程进度的动态图标显示，根据当前流程步骤（step）动态选择左侧图标，体现不同阶段的专业角色。

## 📋 流程步骤与图标映射

### 流程定义

根据 `src/pages/Trend/constants.tsx` 中的 `agentTaskMap` 定义：

| Step | 角色标题 | 角色描述 | 图标文件 | 图标标识符 |
|------|----------|----------|----------|------------|
| step1 | Research Analyst | Research the market and the product | researchAnalyst.png | researchAnalyst |
| step2 | Brand Strategist | Strategize the marketing plan | brandStrategist.png | brandStrategist |
| step3 | Creative Director | Create the creative content | creativeDirector.png | creativeDirector |
| step4 | Content Manager | Manage the content | OperationsManager.png | operationsManager |

### 默认图标

- 对于 step5-step8 或其他未定义的步骤，使用默认的 `Bot` 图标

## 🔧 技术实现

### 1. 动态图标选择逻辑

在 `src/pages/Trend/stores/addChat.ts` 中添加了 `getLeftIconByStep` 函数：

```typescript
const getLeftIconByStep = (currentStep: StepNum): string => {
  switch (currentStep) {
    case 'step1':
      return 'researchAnalyst' // 研究分析师
    case 'step2':
      return 'brandStrategist' // 品牌策略师
    case 'step3':
      return 'creativeDirector' // 创意总监
    case 'step4':
      return 'operationsManager' // 运营经理
    default:
      return 'Bot' // 默认使用 Bot 图标
  }
}
```

### 2. 卡片配置更新

所有 5 张策略卡片的左侧图标配置都更新为：

```typescript
leftIcon: {
  show: true,
  icon: currentLeftIcon, // 根据流程步骤动态选择图标
  size: 'lg' as const,
}
```

### 3. 图标渲染支持

在 `src/pages/Trend/components/MessageComponents/MessageContent.tsx` 的 `renderIcon` 函数中添加了对新图标的支持：

```typescript
case 'researchAnalyst':
  return (
    <img
      src={new URL('@/assets/image/home/<USER>', import.meta.url).href}
      alt="research analyst"
      className={cn('h-12 w-12', className)}
    />
  )
// ... 其他图标类似处理
```

## 📁 文件结构要求

### 图标文件位置

图标文件已存在于 `src/assets/image/home/<USER>

```text
src/assets/image/home/
├── researchAnalyst.png     # 研究分析师图标 ✅
├── brandStrategist.png     # 品牌策略师图标 ✅
├── creativeDirector.png    # 创意总监图标 ✅
└── OperationsManager.png   # 运营经理图标 ✅
```

### 图标规格建议

- **格式**：PNG（支持透明背景）
- **尺寸**：建议 48x48px 或更高分辨率
- **样式**：与现有 UI 风格保持一致
- **背景**：透明背景，便于适配不同主题

## 🔄 工作流程

### 动态图标显示逻辑

1. **流程开始**：用户触发 Trend 分析流程
2. **步骤执行**：系统按顺序执行 step1 → step2 → step3 → step4
3. **图标更新**：每个步骤完成后，调用 `createReportCard` 函数
4. **图标选择**：根据当前 `step` 参数动态选择对应的专业角色图标
5. **卡片显示**：5张策略卡片都显示相同的当前步骤对应的左侧图标

### 示例场景

- **Step1 执行时**：所有卡片左侧显示"研究分析师"图标
- **Step2 执行时**：所有卡片左侧显示"品牌策略师"图标
- **Step3 执行时**：所有卡片左侧显示"创意总监"图标
- **Step4 执行时**：所有卡片左侧显示"运营经理"图标

## 📝 修改的文件

### 1. `src/pages/Trend/stores/addChat.ts`

- ✅ 添加 `getLeftIconByStep` 函数
- ✅ 更新所有策略卡片的 `leftIcon` 配置
- ✅ 实现基于 step 参数的动态图标选择

### 2. `src/pages/Trend/components/MessageComponents/MessageContent.tsx`

- ✅ 扩展 `renderIcon` 函数
- ✅ 添加 4 个新的 PNG 图标处理分支
- ✅ 设置合适的图标尺寸（h-12 w-12）

## 🎨 视觉效果

### 左侧图标（动态）

- **尺寸**：大尺寸（lg）- 48x48px
- **位置**：卡片左侧
- **功能**：体现当前流程阶段的专业角色

### 右侧图标（静态）

- **保持不变**：每张卡片的右侧图标配置保持原有的个性化设置
- **尺寸**：中等尺寸（md）- 24x24px
- **功能**：装饰性图标，区分不同卡片类型

## ✅ 验证步骤

### 开发环境测试

1. ✅ 图标文件已存在于 `src/assets/image/home/<USER>
2. ✅ 图标路径已修复为正确的路径
3. 启动开发服务器：`pnpm dev`
4. 访问 Trend 页面并开始分析流程
5. 观察每个步骤完成后卡片的左侧图标变化

### 预期结果

- **Step1**：卡片显示研究分析师图标
- **Step2**：卡片显示品牌策略师图标
- **Step3**：卡片显示创意总监图标
- **Step4**：卡片显示运营经理图标
- **图标加载**：所有图标正常加载，无 404 错误
- **样式一致**：图标尺寸和位置符合设计要求

## 🚨 注意事项

### 图标文件要求

1. **文件命名**：必须与代码中的标识符完全匹配
2. **文件格式**：PNG 格式，支持透明背景
3. **文件路径**：确保放置在正确的 `src/assets/image/` 目录下

### 向后兼容

1. **默认图标**：未定义的步骤仍使用 Bot 图标
2. **右侧图标**：保持原有的个性化配置不变
3. **现有功能**：不影响其他页面和组件的图标显示

### 性能考虑

1. **图标预加载**：使用 `new URL()` 方式，支持 Vite 的资源优化
2. **缓存机制**：浏览器会自动缓存图标文件
3. **文件大小**：建议优化 PNG 文件大小以提升加载速度

## 🎉 完成状态

✅ 动态图标选择逻辑实现
✅ 策略卡片配置更新完成
✅ 图标渲染支持添加完成
✅ 流程步骤映射关系建立
⏳ 等待图标文件放置到指定目录
⏳ 等待功能测试验证
