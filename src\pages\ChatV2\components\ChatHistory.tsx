import type { AutoScrollAnimateRef } from '@/components/AutoScrollAnimate'
import type { ChatHistoryProps } from '../types'
import { AutoScrollAnimate } from '@/components/AutoScrollAnimate'
import { cn } from '@/utils'
import clsx from 'clsx'
import { AnimatePresence, motion } from 'framer-motion'
import { Loader2 } from 'lucide-react'
import { useEffect, useRef } from 'react'
import { ChatEventBus } from '../constants'
import { ChatMessage } from './ChatMessage'

export const ChatHistory = memo<ChatHistoryProps>(({
  messages,
  onLoadMore,
  hasMore,
  isLoading,
  className,
  ...rest
}) => {
  const observerRef = useRef<IntersectionObserver | null>(null)
  const loadingRef = useRef<HTMLDivElement>(null)
  const autoScrollRef = useRef<AutoScrollAnimateRef>(null)

  useEffect(() => {
    if (loadingRef.current) {
      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasMore) {
            onLoadMore()
          }
        },
        { threshold: 0.5 },
      )

      observerRef.current.observe(loadingRef.current)
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [hasMore, onLoadMore])

  useEffect(
    () => {
      const ref = autoScrollRef.current
      if (!ref)
        return

      if (!ref.isDownScroll()) {
        return
      }
      ref.scrollToBottom()
    },
    [messages],
  )

  useEffect(
    () => {
      ChatEventBus.on('scrollToBottom', () => {
        autoScrollRef.current?.scrollToBottom()
      })

      return () => {
        ChatEventBus.off('scrollToBottom')
      }
    },
    [],
  )

  return (
    <AutoScrollAnimate
      updateBy={ messages }
      ref={ autoScrollRef }
      fadeInMask={ false }
      className={ cn('chat-history flex flex-col gap-14 overflow-y-auto overflow-x-hidden p-6', className) }
      { ...rest }
    >
      { hasMore && (
        <motion.div
          ref={ loadingRef }
          initial={ { opacity: 0 } }
          animate={ { opacity: 1 } }
          className="flex items-center justify-center"
        >
          <motion.div
            animate={ { rotate: 360 } }
            transition={ { duration: 1, repeat: Infinity, ease: 'linear' } }
          >
            <Loader2 className="h-6 w-6 text-violet-500" />
          </motion.div>
        </motion.div>
      ) }
      <AnimatePresence initial={ false }>
        { messages.map(message => (
          <ChatMessage
            key={ message.id }
            message={ message }
            isLoading={ message.type === 'loading' && isLoading }
          />
        )) }
      </AnimatePresence>
    </AutoScrollAnimate>
  )
})
