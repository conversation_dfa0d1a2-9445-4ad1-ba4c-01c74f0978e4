import type { StateStoreType } from '../../stores'
import type { ChatMessage } from '../../types'
import { AnimateShow } from '@/components/Animate/AnimateShow'
import { Checkmark } from '@/components/Checkbox'
import { Icon } from '@/components/Icon'
import { LoadingIcon } from '@/components/Loading/LoadingIcon'
import { useInsertStyle, useTheme } from '@/hooks'
import { cn, mergeClassNameWithDefaultWidth } from '@/utils'
import { formatDate } from '@jl-org/tool'
import { motion } from 'framer-motion'
import { ChevronDown, Sparkles, Trash2 } from 'lucide-react'
import { memo, useState } from 'react'
import { MessageContent } from './MessageContent'

export const MessageItem = memo<MessageItemProps>(({
  message,
  onDelete,
  className,
  stateStore,
  ...props
}) => {
  const isUser = message.sender === 'user'
  const bgCls = [
    'bg-slate-50 dark:bg-slate-800',
    isUser
      ? 'bg-slate-200 dark:bg-slate-700 text-slate-800 dark:text-slate-200 rounded-tr-none'
      : 'text-slate-800 dark:text-slate-200',
    message.type === 'thinking-start' && 'bg-slate-50/70 dark:bg-slate-800/20 text-slate-500 dark:text-slate-400 border border-slate-200 dark:border-slate-700/50',
    message.type === 'thinking-end' && 'bg-slate-50/70 dark:bg-slate-800/20 text-slate-500 dark:text-slate-400 border border-slate-200 dark:border-slate-700/50',
    message.type === 'loading' && 'bg-slate-100 text-slate-500 dark:bg-slate-800 dark:text-slate-400',
    message.type === 'code' && 'p-0',
  ]

  const [theme] = useTheme()
  useInsertStyle(`
    .markdown-body {
      background-color: ${theme === 'light'
        ? '#fff'
        : '#0F172A'} !important;
    }
  `)

  const [thinkingExpanded, setThinkingExpanded] = useState(true)

  return (
    <div
      className={ cn(
        mergeClassNameWithDefaultWidth(
          'w-full',
          className,
          'flex group',
          isUser
            ? 'justify-end'
            : 'justify-center',
        ),
      ) }
    >
      { isUser
        ? <div
            className={ cn(
              message.type === 'card' || message.type === 'code'
                ? 'rounded-2xl relative'
                : 'p-3 rounded-2xl relative',
              message.type === 'card' && [
                'bg-white dark:bg-gray-800',
                message.card?.variant === 'info' && 'border-l-4 border-l-blue-500',
                message.card?.variant === 'success' && 'border-l-4 border-l-green-500',
                message.card?.variant === 'warning' && 'border-l-4 border-l-amber-500',
                message.card?.variant === 'error' && 'border-l-4 border-l-red-500',
                (!message.card?.variant || message.card?.variant === 'default') && 'border-l-4 border-l-emerald-500',
              ],
              message.type !== 'card' && message.type !== 'code' && bgCls,
            ) }
          >
            <div className="space-y-3">
              <MessageContent isUser message={ message } stateStore={ stateStore } />
            </div>

            { message.type !== 'card' && message.type !== 'code' && (
              <div className="mt-1 text-right text-xs opacity-70">
                { formatDate('HH:mm') }
              </div>
            ) }
          </div>

        : <div className="w-full">
            { (message.type === 'thinking-start' || message.type === 'thinking-end') && (
              <div className="mb-2 flex flex-col space-y-2">
                <div
                  className="w-fit flex cursor-pointer items-center rounded-2xl bg-slate-100 px-3 py-1.5 text-slate-500 space-x-2 dark:bg-slate-600 dark:text-slate-300"
                  onClick={ () => setThinkingExpanded(!thinkingExpanded) }
                >
                  <Sparkles size={ 14 } className="text-slate-400 dark:text-slate-400" />
                  <span className="text-xs font-medium">
                    { message.type === 'thinking-start'
                      ? 'Thinking...'
                      : 'Thinking done' }
                  </span>

                  { message.type === 'thinking-start'
                    ? <LoadingIcon size={ 14 } />
                    : <Checkmark size={ 14 } /> }
                  <motion.div
                    animate={ {
                      rotate: thinkingExpanded
                        ? 180
                        : 0,
                    } }
                    transition={ { duration: 0.2 } }
                    className="ml-auto"
                  >
                    <ChevronDown className="h-3 w-3 text-slate-400" />
                  </motion.div>
                </div>

                <AnimateShow
                  show={ thinkingExpanded }
                  variants={ {
                    initial: { opacity: 1, height: 'auto' },
                    animate: { opacity: 1, height: 'auto' },
                    exit: { opacity: 0, height: 0 },
                  } }
                >
                  <div className="whitespace-pre-wrap border-l border-slate-200 pl-2 text-xs text-slate-500 dark:border-slate-700 dark:text-slate-400">
                    { message.content }
                  </div>
                </AnimateShow>
              </div>
            ) }

            { message.type === 'loading' && (
              <div className="flex items-center p-2 space-x-2">
                <motion.div
                  className="flex space-x-1"
                  initial={ { opacity: 0.3 } }
                  animate={ { opacity: 1 } }
                  transition={ { duration: 0.8, repeat: Infinity, repeatType: 'reverse' } }
                >
                  <motion.div
                    className="h-2 w-2 rounded-full bg-current text-slate-500 dark:text-slate-400"
                    animate={ { y: [0, -5, 0] } }
                    transition={ { duration: 0.8, repeat: Infinity, repeatDelay: 0.2 } }
                  />
                  <motion.div
                    className="h-2 w-2 rounded-full bg-current text-slate-500 dark:text-slate-400"
                    animate={ { y: [0, -5, 0] } }
                    transition={ { duration: 0.8, delay: 0.2, repeat: Infinity, repeatDelay: 0.2 } }
                  />
                  <motion.div
                    className="h-2 w-2 rounded-full bg-current text-slate-500 dark:text-slate-400"
                    animate={ { y: [0, -5, 0] } }
                    transition={ { duration: 0.8, delay: 0.4, repeat: Infinity, repeatDelay: 0.2 } }
                  />
                </motion.div>
                <span className="ml-4 text-sm text-slate-500 dark:text-slate-400">{ message.content || 'Loading...' }</span>
              </div>
            ) }

            <div className="space-y-3">
              <MessageContent isUser={ false } message={ message } stateStore={ stateStore } />
            </div>

            { onDelete
              && <Icon
                icon={ Trash2 }
                size={ 14 }
                className="absolute top-1 size-6 opacity-0 -right-8 group-hover:opacity-100"
                onClick={ () => onDelete?.(message.id) }
              /> }
          </div> }
    </div>
  )
})

MessageItem.displayName = 'MessageItem'

export type MessageItemProps = {
  message: ChatMessage
  onDelete?: (messageId: string) => void
  /** 自定义样式类名 */
  className?: string
  stateStore: StateStoreType
}
& React.PropsWithChildren<React.HTMLAttributes<HTMLDivElement>>
