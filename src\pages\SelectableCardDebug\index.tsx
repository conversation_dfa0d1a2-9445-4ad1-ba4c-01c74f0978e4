import type { ChatMessage } from '@/pages/Trend/types'
import { MessageItem } from '@/pages/Trend/components/MessageItem'
import { useState } from 'react'

/** 生成5个测试卡片数据 */
function generateTestMessages(): ChatMessage[] {
  return [
    /** 第1个卡片 - 默认样式 */
    {
      id: 'card-1',
      sender: 'assistant',
      type: 'card',
      content: '第1个卡片',
      timestamp: Date.now(),
      meta: { step: 'step1' },
      card: {
        title: '第1个卡片',
        description: '这是第1个卡片，使用默认样式。',
        content: (
          <div className="p-4">
            <p className="text-gray-600">第1个卡片内容</p>
          </div>
        ),
      },
    },
    /** 第2个卡片 - 自定义宽度高度 w-106.5 */
    {
      id: 'card-2',
      sender: 'assistant',
      type: 'card',
      content: '第2个卡片',
      timestamp: Date.now(),
      meta: { step: 'step2' },
      card: {
        title: '第2个卡片',
        description: '这是第2个卡片，自定义宽度高度为 w-106.5。',
        content: (
          <div className="p-4">
            <p className="text-gray-600">第2个卡片内容 - 自定义尺寸</p>
          </div>
        ),
      },
    },
    /** 第3个卡片 - 默认样式 */
    {
      id: 'card-3',
      sender: 'assistant',
      type: 'card',
      content: '第3个卡片',
      timestamp: Date.now(),
      meta: { step: 'step3' },
      card: {
        title: '第3个卡片',
        description: '这是第3个卡片，使用默认样式。',
        content: (
          <div className="p-4">
            <p className="text-gray-600">第3个卡片内容</p>
          </div>
        ),
      },
    },
    /** 第4个卡片 - 默认样式 */
    {
      id: 'card-4',
      sender: 'assistant',
      type: 'card',
      content: '第4个卡片',
      timestamp: Date.now(),
      meta: { step: 'step4' },
      card: {
        title: '第4个卡片',
        description: '这是第4个卡片，使用默认样式。',
        content: (
          <div className="p-4">
            <p className="text-gray-600">第4个卡片内容</p>
          </div>
        ),
      },
    },
    /** 第5个卡片 - 默认样式 */
    {
      id: 'card-5',
      sender: 'assistant',
      type: 'card',
      content: '第5个卡片',
      timestamp: Date.now(),
      meta: { step: 'step5' },
      card: {
        title: '第5个卡片',
        description: '这是第5个卡片，使用默认样式。',
        content: (
          <div className="p-4">
            <p className="text-gray-600">第5个卡片内容</p>
          </div>
        ),
      },
    },
  ]
}

export default function SelectableCardDebug() {
  const [darkMode, setDarkMode] = useState(false)
  const testMessages = generateTestMessages()

  return (
    <div className={ `min-h-screen transition-colors ${darkMode
      ? 'dark bg-gray-900'
      : 'bg-gray-50'}` }>
      {/* 控制面板 */}
      <div className="sticky top-0 z-10 border-b border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
        <div className="mx-auto max-w-4xl">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl text-gray-900 font-bold dark:text-white">
              五个卡片测试 - 第二个卡片自定义宽度
            </h1>
            <label className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
              <input
                type="checkbox"
                checked={ darkMode }
                onChange={ e => setDarkMode(e.target.checked) }
                className="rounded"
              />
              暗黑模式
            </label>
          </div>
        </div>
      </div>

      {/* 卡片展示区域 */}
      <div className="mx-auto max-w-4xl p-6">
        <div className="space-y-6">
          {testMessages.map((message, index) => (
            <div key={ message.id }>
              <h3 className="mb-2 text-sm text-gray-600 dark:text-gray-400">
                第
                {index + 1}
                个卡片
                {' '}
                {index === 1
                  ? '(自定义宽度 w-106.5)'
                  : '(默认样式)'}
              </h3>
              <MessageItem
                message={ message }
                cardClassName={ index === 1
                  ? 'w-106.5'
                  : undefined } // 👈 第2个卡片设置自定义宽度
                stateStore={ {} as any }
              />
            </div>
          ))}
        </div>

        {/* 说明 */}
        <div className="mt-8 border border-gray-200 rounded-lg bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
          <h2 className="mb-4 text-lg text-gray-900 font-semibold dark:text-white">
            修改说明
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            第2个卡片通过
            {' '}
            <code className="rounded bg-gray-100 px-1 dark:bg-gray-700">cardClassName="w-106.5"</code>
            设置了自定义宽度，其他卡片使用默认样式。
          </p>
        </div>
      </div>
    </div>
  )
}
