import type { AutoScrollAnimateRef } from '@/components/AutoScrollAnimate'
import type { MessageStoreType, StateStoreType, TaskStoreType } from '../stores'
import type { ChatMessage } from '../types'
import { AutoScrollAnimate } from '@/components/AutoScrollAnimate'
import { cn } from '@/utils'
import { forwardRef, memo, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'
import { DistributionEvent, eventBus } from '../constants'
import { MessageItem } from './MessageItem'

export const ChatHistory = memo(forwardRef<{ scrollToBottom: () => void }, ChatHistoryProps>((
  {
    style,
    className,
    onDeleteMessage,

    taskStore,
    messageStore,
    stateStore,
  },
  ref,
) => {
  const autoScrollRef = useRef<AutoScrollAnimateRef>(null)
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null)

  const currentStep = taskStore.useAndDispose().currentStep
  const { messages } = messageStore.useAndDispose()
  const msgs = useMemo(
    () => {
      const msgs = messages.filter(item => item.meta?.step === currentStep)
      const cardMsgs = msgs.filter(item => item.type === 'card')
      const otherMsgs = msgs.filter(item => item.type !== 'card')
      return [...otherMsgs, ...cardMsgs]
    },
    [messages, currentStep],
  )

  useImperativeHandle(ref, () => ({
    scrollToBottom: () => {
      autoScrollRef.current?.scrollToBottom()
      autoScrollRef.current?.setAutoScroll(true)
    },
  }), [])

  useEffect(
    () => {
      eventBus.on(DistributionEvent.ScrollToBottom, () => {
        autoScrollRef.current?.scrollToBottom()
        autoScrollRef.current?.setAutoScroll(true)
      })

      return () => {
        eventBus.off(DistributionEvent.ScrollToBottom)
      }
    },
    [],
  )

  return <AutoScrollAnimate
    className={ cn(
      'space-y-6',
      'flex flex-col items-start',
      className,
    ) }
    style={ style }
    updateBy={ msgs }
    fadeInMask={ false }
    ref={ autoScrollRef }
  >
    { msgs.map((message, index) => {
      const prevMessage = index > 0
        ? msgs[index - 1]
        : null
      const isSameSender = prevMessage && prevMessage.sender === message.sender

      return (
        <MessageItem
          key={ message.id }
          message={ message as ChatMessage }
          onDelete={ () => onDeleteMessage?.(message.id) }
          className={ cn(
            'w-full',
            isSameSender
              ? 'mt-2'
              : '',
          ) }
          stateStore={ stateStore }
          isSelected={ selectedMessageId === message.id }
          onSelectChange={ messageId => setSelectedMessageId(messageId) }
        />
      )
    }) }
  </AutoScrollAnimate>
}))

ChatHistory.displayName = 'ChatHistory'

export type ChatHistoryProps = {
  /**
   * 删除消息时触发
   */
  onDeleteMessage?: (messageId: string) => void

  taskStore: TaskStoreType
  messageStore: MessageStoreType
  stateStore: StateStoreType
} & React.HTMLAttributes<HTMLDivElement>
