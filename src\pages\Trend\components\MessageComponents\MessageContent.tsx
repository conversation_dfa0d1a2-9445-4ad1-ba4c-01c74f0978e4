import type { CodeMirrorLanguage } from '@/components/CodeMirrorEditor'
import type { StateStoreType } from '../../stores'
import type { ChatMessage } from '../../types'
import { Button } from '@/components/Button'
import { GlowBorder, SelectableGradientCard } from '@/components/Card'
import { CodeMirrorEditor } from '@/components/CodeMirrorEditor'
import { Icon } from '@/components/Icon'
import { MdToHtml } from '@/components/MdEditor/MdToHtml'
import { cn } from '@/utils'
import { Award, BarChart3, Bot, Calendar, Camera, Crown, Download, Eye, FileText, Gift, Heart, Image, Lightbulb, MessageCircle, Music, Palette, Paperclip, Rocket, Share2, Smartphone, Star, Target, TrendingUp, Users, Video, Zap } from 'lucide-react'
import { memo } from 'react'
import { formatFileSize } from '../../tool'
import { BijiDataContent } from './BijiDataContent'
import { BiJiListContent } from './BiJiListContent'
import { DarenDataContent } from './DarenDataContent'
import { DaRenListContent } from './DaRenListContent'

/** 渲染图标的辅助函数 */
function renderIcon(iconName: string | React.ReactNode, className?: string) {
  /** 如果传入的是 ReactNode，直接返回 */
  if (typeof iconName !== 'string') {
    return iconName
  }

  const iconProps = { className: cn('h-6 w-6', className) }

  switch (iconName) {
    case 'Bot':
      return <Bot { ...iconProps } />
    case 'BarChart3':
      return <BarChart3 { ...iconProps } />
    case 'Smartphone':
      return <Smartphone { ...iconProps } />
    case 'Calendar':
      return <Calendar { ...iconProps } />
    case 'FileText':
      return <FileText { ...iconProps } />
    /** 新增的图标选项 */
    case 'TrendingUp':
      return <TrendingUp { ...iconProps } />
    case 'Users':
      return <Users { ...iconProps } />
    case 'Target':
      return <Target { ...iconProps } />
    case 'Zap':
      return <Zap { ...iconProps } />
    case 'Star':
      return <Star { ...iconProps } />
    case 'Heart':
      return <Heart { ...iconProps } />
    case 'MessageCircle':
      return <MessageCircle { ...iconProps } />
    case 'Share2':
      return <Share2 { ...iconProps } />
    case 'Eye':
      return <Eye { ...iconProps } />
    case 'Camera':
      return <Camera { ...iconProps } />
    case 'Video':
      return <Video { ...iconProps } />
    case 'Music':
      return <Music { ...iconProps } />
    case 'Image':
      return <Image { ...iconProps } />
    case 'Palette':
      return <Palette { ...iconProps } />
    case 'Lightbulb':
      return <Lightbulb { ...iconProps } />
    case 'Rocket':
      return <Rocket { ...iconProps } />
    case 'Crown':
      return <Crown { ...iconProps } />
    case 'Award':
      return <Award { ...iconProps } />
    case 'Gift':
      return <Gift { ...iconProps } />
    /** 自定义 SVG 图标 */
    case 'card-right-icon1':
      return (
        <img
          src={ new URL('@/assets/svg/card-right-icon1.svg', import.meta.url).href }
          alt="document icon"
          className={ cn('h-6 w-6', className) }
        />
      )
    case 'card-right-icon2':
      return (
        <img
          src={ new URL('@/assets/svg/card-right-icon2.svg', import.meta.url).href }
          alt="chart icon"
          className={ cn('h-6 w-6', className) }
        />
      )
    case 'card-right-icon3':
      return (
        <img
          src={ new URL('@/assets/svg/card-right-icon3.svg', import.meta.url).href }
          alt="mobile icon"
          className={ cn('h-6 w-6', className) }
        />
      )
    case 'card-right-icon4':
      return (
        <img
          src={ new URL('@/assets/svg/card-right-icon4.svg', import.meta.url).href }
          alt="calendar icon"
          className={ cn('h-6 w-6', className) }
        />
      )
    case 'rightIcon':
      return (
        <img
          src={ new URL('@/assets/svg/rightIcon.svg', import.meta.url).href }
          alt="arrow icon"
          className={ cn('h-6 w-6', className) }
        />
      )
    /** 动态流程图标 - PNG 格式 */
    case 'researchAnalyst':
      return (
        <img
          src={ new URL('@/assets/image/home/<USER>', import.meta.url).href }
          alt="research analyst"
          className={ cn('h-12.5 w-12.5', className) }
        />
      )
    case 'brandStrategist':
      return (
        <img
          src={ new URL('@/assets/image/home/<USER>', import.meta.url).href }
          alt="brand strategist"
          className={ cn('h-12 w-12', className) }
        />
      )
    case 'creativeDirector':
      return (
        <img
          src={ new URL('@/assets/image/home/<USER>', import.meta.url).href }
          alt="creative director"
          className={ cn('h-12 w-12', className) }
        />
      )
    case 'operationsManager':
      return (
        <img
          src={ new URL('@/assets/image/home/<USER>', import.meta.url).href }
          alt="operations manager"
          className={ cn('h-12 w-12', className) }
        />
      )
    default:
      return null
  }
}

/** 渲染自定义内容的辅助函数 */
function renderCustomContent(contentType: string) {
  switch (contentType) {
    case 'TrendTopicBox':
      return (
        <div className="mt-3 border border-gray-200 rounded-lg bg-gray-50 p-3">
          <div className="text-sm text-gray-600">
            Custom trend topic content would go here
          </div>
        </div>
      )
    default:
      return null
  }
}

export const MessageContent = memo<{
  message: ChatMessage
  isUser: boolean
  stateStore: StateStoreType
  isSelected?: boolean
  onSelectChange?: (messageId: string | null) => void
  /** 自定义卡片样式类名 */
  cardClassName?: string
}>(({ message, isUser, stateStore, isSelected: propIsSelected, onSelectChange, cardClassName }) => {
      const bgCls = [
        'bg-slate-50 dark:bg-slate-800',
        isUser
          ? 'bg-slate-200 dark:bg-slate-700 text-slate-800 dark:text-slate-200 rounded-tr-none'
          : 'text-slate-800 dark:text-slate-200',
      ]

      // Bot 头像数组，根据 step 选择对应的头像
      const avatarArr = [
        new URL('../../assets/Bot=Research Analyst Bot.png', import.meta.url).href,
        new URL('../../assets/Bot=Strategy Planner Bot.png', import.meta.url).href,
        new URL('../../assets/Bot=Creative Lead Bot.png', import.meta.url).href,
        new URL('../../assets/Bot=Content Manager.png', import.meta.url).href,
      ]

      /** 根据 step 获取对应的头像索引 */
      const getAvatarIndex = (step: string) => {
        switch (step) {
          case 'step1':
            return 0 // Research Analyst Bot
          case 'step2':
            return 1 // Strategy Planner Bot
          case 'step3':
            return 2 // Creative Lead Bot
          case 'step4':
            return 3 // Content Manager
          default:
            return 0 // 默认使用第一个
        }
      }

      const avatarIndex = getAvatarIndex(message.meta?.step || 'step1')
      const currentAvatar = avatarArr[avatarIndex]

      return (
        <>
          { message.content && message.type === 'text' && (
            <div className={ cn(
              'whitespace-pre-wrap break-words',
              !isUser && 'py-2 text-slate-800 dark:text-slate-200',
            ) }>
              { message.content }
            </div>
          ) }

          { /* Markdown 内容 */ }
          { message.type === 'markdown' && (
            <MdToHtml
              content={ message.content }
              className={ cn(
                isUser
                  ? bgCls
                  : 'py-2 text-slate-800 dark:text-slate-200',
              ) }
            />
          ) }

          { message.type === 'code' && (
            <CodeMirrorEditor
              code={ message.content }
              language={ (message.meta?.language as string || 'html') as CodeMirrorLanguage }
              copyable
              readOnly
              className="max-h-xl"
            />
          ) }

          { message.type === 'biji_data' && message.biji_data && message.biji_data.length > 0
            && <BijiDataContent isUser={ isUser } bijiList={ message.biji_data } /> }

          { message.type === 'daren_data' && message.daren_data && message.daren_data.length > 0
            && <DarenDataContent isUser={ isUser } darenList={ message.daren_data } /> }

          { message.type === 'biji_list' && message.biji_list && Object.keys(message.biji_list).length > 0
            && <BiJiListContent isUser={ isUser } items={ message.biji_list } stateStore={ stateStore } /> }

          { message.type === 'daren_list' && message.daren_list && Object.keys(message.daren_list).length > 0
            && <DaRenListContent isUser={ isUser } items={ message.daren_list } stateStore={ stateStore } /> }

          { message.images && message.images.length > 0 && (
            <div className={ cn(
              'flex flex-wrap gap-2',
              !isUser && 'rounded-lg bg-slate-100 py-2 dark:bg-slate-800',
            ) }>
              { message.images.map((image, index) => (
                <div
                  key={ `${image.url}-${index}` }
                  className="flex flex-1 flex-col"
                >
                  <img
                    src={ image.url }
                    alt={ image.caption || 'Image' }
                    className={ cn(
                      'rounded-lg',
                      isUser && 'max-h-[200px] max-w-full',
                    ) }
                  />

                  { image.caption && (
                    <div className="mt-1 text-center text-xs opacity-80">{ image.caption }</div>
                  ) }
                </div>
              )) }
            </div>
          ) }

          { message.files && message.files.length > 0 && (
            <div className={ cn('flex flex-col space-y-2', !isUser && 'py-2') }>
              { message.files.map((file, index) => (
                <div
                  key={ index }
                  className="flex items-center rounded-lg bg-slate-200/50 p-2 ring-slate-300 transition-all duration-300 space-x-3 dark:bg-slate-700/30 hover:ring-.5 dark:ring-slate-700"
                >
                  <div className="h-10 w-10 flex items-center justify-center rounded-lg bg-slate-200 text-slate-500 dark:bg-slate-700 dark:text-slate-400">
                    <Paperclip strokeWidth={ 1.5 } />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="truncate text-sm font-medium">{ file.name }</div>
                    <div className="text-xs opacity-70">{ formatFileSize(file.size) }</div>
                  </div>

                  <Icon asChild>
                    <a
                      href={ file.url }
                      download={ file.name }
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Download strokeWidth={ 1.5 } size={ 18 } />
                    </a>
                  </Icon>
                </div>
              )) }
            </div>
          ) }

          { message.type === 'card' && message.card && (
            isUser
              ? (
                  <GlowBorder
                    spin={ false }
                  >
                    {/* 卡片头部 */ }
                    { (message.card.title || message.card.icon) && (
                      <div className="flex items-center p-4 pb-2 space-x-3">
                        { message.card.icon && (
                          <div className={ cn(
                            'flex items-center justify-center',
                            message.card.variant === 'info' && 'text-blue-600 dark:text-blue-400',
                            message.card.variant === 'success' && 'text-green-600 dark:text-green-400',
                            message.card.variant === 'warning' && 'text-amber-600 dark:text-amber-400',
                            message.card.variant === 'error' && 'text-red-600 dark:text-red-400',
                            (!message.card.variant || message.card.variant === 'default') && 'text-slate-600 dark:text-slate-400',
                          ) }>
                            { message.card.icon }
                          </div>
                        ) }
                        { message.card.title && (
                          <h3 className={ cn(
                            'font-semibold text-sm',
                            message.card.variant === 'info' && 'text-blue-900 dark:text-blue-100',
                            message.card.variant === 'success' && 'text-green-900 dark:text-green-100',
                            message.card.variant === 'warning' && 'text-amber-900 dark:text-amber-100',
                            message.card.variant === 'error' && 'text-red-900 dark:text-red-100',
                            (!message.card.variant || message.card.variant === 'default') && 'text-slate-900 dark:text-slate-100',
                          ) }>
                            { message.card.title }
                          </h3>
                        ) }
                      </div>
                    ) }

                    {/* 卡片描述 */ }
                    { message.card.description && (
                      <div className={ cn(
                        'px-4 text-sm',
                        !message.card.title && !message.card.icon && 'pt-4',
                        message.card.variant === 'info' && 'text-blue-700 dark:text-blue-300',
                        message.card.variant === 'success' && 'text-green-700 dark:text-green-300',
                        message.card.variant === 'warning' && 'text-amber-700 dark:text-amber-300',
                        message.card.variant === 'error' && 'text-red-700 dark:text-red-300',
                        (!message.card.variant || message.card.variant === 'default') && 'text-slate-600 dark:text-slate-300',
                      ) }>
                        { message.card.description }
                      </div>
                    ) }

                    {/* 卡片内容 */ }
                    { message.card.content && (
                      <div className={ cn(
                        'px-4',
                        !message.card.title && !message.card.icon && !message.card.description && 'pt-4',
                        message.card.description && 'pt-2',
                        (!message.card.description && (message.card.title || message.card.icon)) && 'pt-1',
                      ) }>
                        { message.card.content }
                      </div>
                    ) }

                    {/* 卡片操作按钮 */ }
                    { message.card.actions && message.card.actions.length > 0 && (
                      <div className="flex flex-wrap gap-2 p-4 pt-3">
                        { message.card.actions.map((action, index) => (
                          <Button
                            key={ `${action.label}-${index}` }
                            onClick={ action.onClick }
                            disabled={ action.disabled }
                            variant={ action.type }
                            designStyle={ action.type === 'info'
                              ? 'outlined'
                              : undefined }
                            size="sm"
                          >
                            { action.label }
                          </Button>
                        )) }
                      </div>
                    ) }
                  </GlowBorder>
                )
              : (
                  <SelectableGradientCard
                    className={ cn(
                      'cursor-pointer',
                      { 'cursor-pointer': !!message.card.onClick },
                      cardClassName, // 👈 添加自定义卡片样式
                    ) }
                    borderGradient="linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)"
                    hoverBackground="linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)"
                    selectedBackground="linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)"
                    selected={ propIsSelected }
                    onSelectedChange={ (selected) => {
                      /** 如果当前已选中，则取消选中；否则选中当前卡片 */
                      onSelectChange?.(selected
                        ? message.id
                        : null)
                      message.card?.onClick?.()
                    } }
                    contentClassName={ message.card.cardConfig
                      ? undefined
                      : 'flex items-center justify-between' }
                    cardConfig={ message.card.cardConfig
                      ? {
                          ...message.card.cardConfig,
                          leftIcon: message.card.cardConfig.leftIcon
                            ? {
                                ...message.card.cardConfig.leftIcon,
                                icon: renderIcon(message.card.cardConfig.leftIcon.icon as string, 'h-12 w-12 text-blue-500'),
                              }
                            : undefined,
                          rightIcon: message.card.cardConfig.rightIcon
                            ? {
                                ...message.card.cardConfig.rightIcon,
                                icon: renderIcon(message.card.cardConfig.rightIcon.icon as string, 'h-6 w-6 text-gray-400'),
                              }
                            : undefined,
                          content: message.card.cardConfig.content
                            ? {
                                ...message.card.cardConfig.content,
                                customContent: message.card.cardConfig.content.customContent
                                  ? renderCustomContent(message.card.cardConfig.content.customContent as string)
                                  : undefined,
                              }
                            : undefined,
                        }
                      : undefined }
                  >
                    {/* 只在没有 cardConfig 时显示原有的卡片布局 */}
                    { !message.card.cardConfig && (
                      <>
                        <div className="flex-shrink-0 p-4 pr-3">
                          <img src={ currentAvatar } alt="bot-avatar" className="h-12 w-12 rounded-full" />
                        </div>
                        <div className="w-full flex-1 py-4">
                          {/* 卡片头部 */ }
                          { (message.card.title || message.card.icon) && (
                            <div className="flex items-center pb-2 space-x-3">
                              { message.card.title && (
                                <h3 className="font-bold">
                                  { message.card.title }
                                </h3>
                              ) }
                            </div>
                          ) }

                          {/* 卡片描述 */ }
                          { message.card.description && (
                            <div className={ cn(
                              'text-sm text-gray-600',
                            ) }>
                              { message.card.description }
                            </div>
                          ) }

                          {/* 卡片内容 */ }
                          { message.card.content && (
                            <div className={ cn(
                              'px-4',
                              !message.card.title && !message.card.icon && !message.card.description && 'pt-4',
                              message.card.description && 'pt-2',
                              (!message.card.description && (message.card.title || message.card.icon)) && 'pt-1',
                            ) }>
                              { message.card.content }
                            </div>
                          ) }

                          {/* 卡片操作按钮 */ }
                          { message.card.actions && message.card.actions.length > 0 && (
                            <div className="flex flex-wrap gap-2 px-4 pt-4">
                              { message.card.actions.map((action, index) => (
                                <Button
                                  key={ `${action.label}-${index}` }
                                  onClick={ action.onClick }
                                  disabled={ action.disabled }
                                  variant={ action.type }
                                  designStyle={ action.type === 'info'
                                    ? 'outlined'
                                    : undefined }
                                  size="sm"
                                >
                                  { action.label }
                                </Button>
                              )) }
                            </div>
                          ) }
                        </div>
                        { !message.card.content && (
                          <div className="flex-shrink-0 self-start p-4">
                            <img src={ new URL('@/assets/svg/doc-card2.svg', import.meta.url).href } alt="doc-icon" className="h-6 w-6" />
                          </div>
                        ) }
                      </>
                    ) }
                  </SelectableGradientCard>
                )
          ) }
        </>
      )
    })
MessageContent.displayName = 'MessageContent'
