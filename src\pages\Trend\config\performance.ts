/**
 * Trend 模块性能优化配置
 */

export const TrendPerformanceConfig = {
  /** 是否启用快速模式（开发环境默认启用） */
  fastMode: import.meta.env.DEV,

  /** 打字机效果配置 */
  typewriter: {
    /** 快速模式下的延迟配置 */
    fast: {
      delayPerChar: 0.01,
      minDelay: 0.1,
      maxDelay: 0.5,
    },
    /** 正常模式下的延迟配置 */
    normal: {
      delayPerChar: 0.1,
      minDelay: 1,
      maxDelay: 3,
    },
  },

  /** Thinking 阶段更新配置 */
  thinking: {
    /** 阶段更新间隔（毫秒） - 开发环境加速 */
    stageUpdateInterval: import.meta.env.DEV ? 300 : 1500,
    /** 初始内容 */
    initialContent: `🚀 正在启动 AI 分析引擎...
📊 准备处理品牌数据和行业信息...
🔍 开始深度分析市场趋势...`,
    /** 阶段性内容 */
    stages: [
      '🔄 正在连接数据源...',
      '📈 分析行业竞争格局...',
      '🎯 评估品牌定位策略...',
      '📝 生成专业分析报告...',
    ],
  },

  /** 数据处理优化 */
  dataProcessing: {
    /** 是否按句子分割输出 */
    useSentenceSplit: true,
    /** 长内容阈值 */
    longContentThreshold: 500,
    /** 长内容块大小 */
    longContentChunkSize: 20,
    /** 短内容块大小 */
    shortContentChunkSize: 8,
  },
}

/**
 * 获取当前的延迟配置
 */
export function getDelayConfig() {
  return TrendPerformanceConfig.fastMode
    ? TrendPerformanceConfig.typewriter.fast
    : TrendPerformanceConfig.typewriter.normal
}

/**
 * 计算延迟时间
 */
export function calculateDelay(textLength: number): number {
  const config = getDelayConfig()
  return Math.min(
    Math.max(textLength * config.delayPerChar, config.minDelay),
    config.maxDelay,
  )
}
