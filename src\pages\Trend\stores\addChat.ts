import type { TrendApiResult } from '@/api/TrendApi'
import type { PartRequired } from '@jl-org/ts-tool'
import type { ChatMessage, ReportContentItem, StepNum } from '../types'

import { extractCodeFromMd } from '@/utils'
import { DistributionEvent, eventBus } from '../constants'
import { handleTitle } from '../tool'
import { addReportItem, createBaseMessage, createCardMessage, updateMessageById, updateReportItem } from './create'
import { rmCodeBlock } from './fns'
import { trendAg } from './index'

export async function addMessageByTitle(
  messagesByTitle: TrendApiResult['data'],
  partialMessage: PartRequired<Partial<ChatMessage>, 'meta'>,
  store = trendAg,
) {
  const { messageStore, mapperStore } = store

  if (!messagesByTitle || Object.keys(messagesByTitle).length === 0)
    return

  Object.entries(messagesByTitle).forEach(([k, v]) => {
    if (k === 'biji_data' || k === 'daren_data') {
      try {
        const content = typeof v.content === 'string'
          ? v.content
          : JSON.stringify(v.content)
        const jsonData = JSON.parse(content.trim())

        if (Array.isArray(jsonData) && jsonData.length > 0) {
          const message = createBaseMessage('', {
            type: k as 'biji_data' | 'daren_data',
            ...partialMessage,
            meta: {
              reportTitle: k === 'biji_data'
                ? 'Hot Notes'
                : 'Recommended Influencers',
              ...partialMessage.meta,
            },
            [k]: jsonData,
          })

          mapperStore.nodeTitleToMessageId[k] = message.id
          messageStore.messages.push(message)
        }
      }
      catch (error) {
        console.error(`Failed to parse ${k} data:`, error)
      }
      return
    }

    /** 原有的普通消息处理逻辑 */
    if (!mapperStore.nodeTitleToMessageId[k]) {
      const message = createBaseMessage(v.content, {
        type: 'markdown',
        ...partialMessage,
        meta: {
          reportTitle: k,
          ...partialMessage?.meta,
        },
      })

      mapperStore.nodeTitleToMessageId[k] = message.id
      messageStore.messages.push(message)
    }
    else {
      updateMessageById(mapperStore.nodeTitleToMessageId[k], {
        content: v.content,
      }, store)
    }
  })
}

export function addReportByTitle(
  messagesByTitle: TrendApiResult['data'],
  paritalReport: PartRequired<Partial<ReportContentItem>, 'meta' | 'type'>,
  syncReportId = true,
  store = trendAg,
) {
  const { mapperStore } = store
  const { type } = paritalReport
  const reportItems: ReportContentItem[] = []

  Object.entries(messagesByTitle).forEach(([k, v]) => {
    let content = v.content || ''

    if (!mapperStore.nodeTitleToReportId[k]) {
      if (type === 'code' || type === 'markdown') {
        const code = extractCodeFromMd(content)[0]?.code || ''
        content = code || content
      }

      const reportItem = addReportItem(
        {
          content,
          title: handleTitle(k),
          ...paritalReport,
        },
        true,
        syncReportId,
        store,
      )
      mapperStore.nodeTitleToReportId[k] = reportItem.id
      reportItems.push(reportItem)
    }
    else {
      if (type === 'code' || type === 'markdown') {
        const code = rmCodeBlock(content)
        content = code || content
      }
      updateReportItem(
        mapperStore.nodeTitleToReportId[k],
        {
          content,
        },
        true,
        syncReportId,
        store,
      )
    }
  })

  return {
    reportItems,
    update(content: string, k: string) {
      updateReportItem(
        mapperStore.nodeTitleToReportId[k],
        {
          content,
        },
        true,
        syncReportId,
        store,
      )
    },
  }
}

export function messageToReportItem(
  messages: ChatMessage[],
  reportData: PartRequired<Partial<ReportContentItem>, 'meta'>,
  rmIds?: string[],
  store = trendAg,
) {
  const { messageStore } = store
  const reportItems: ReportContentItem[] = []

  for (let i = 0; i < messages.length; i++) {
    const item = messages[i]
    /** 创建报告�? */
    const reportItem = addReportItem({
      content: item.content,
      title: item.meta?.reportTitle,
      ...reportData,
    }, true, false, store) // 改为 false，不自动打开报告面板

    reportItems.push(reportItem)
  }

  createReportCard(reportItems, reportData.meta.step, store)

  if (rmIds) {
    messageStore.messages = messageStore.messages.filter(item => !rmIds.includes(item.id))
  }
}

export function createReportCard(reportItems: ReportContentItem[], step: StepNum, store = trendAg) {
  const { stateStore, taskStore } = store

  console.log('createReportCard called with:', { reportItems, step })

  /** 根据当前流程步骤动态选择左侧图标 */
  const getLeftIconByStep = (currentStep: StepNum): string => {
    switch (currentStep) {
      case 'step1':
        return 'researchAnalyst' // 研究分析师
      case 'step2':
        return 'brandStrategist' // 品牌策略师
      case 'step3':
        return 'creativeDirector' // 创意总监
      case 'step4':
        return 'operationsManager' // 运营经理
      default:
        return 'Bot' // 默认使用 Bot 图标
    }
  }

  const currentLeftIcon = getLeftIconByStep(step)

  /** 生成 5 张策略卡片，而不是基于 reportItems 的数量 */
  const strategyCards = [
    {
      id: 'competitive-analysis',
      title: 'Competitive Analysis',
      description: 'Analyze market competitors and identify positioning opportunities for your brand.',
      cardConfig: {
        leftIcon: {
          show: true,
          icon: currentLeftIcon, // 根据流程步骤动态选择图标
          size: 'lg' as const,
        },
        rightIcon: {
          show: true,
          icon: 'card-right-icon1', // 使用自定义 SVG 图标
          size: 'md' as const,
        },
        content: {
          title: 'Competitive Analysis',
          description: 'Analyze market competitors and identify positioning opportunities for your brand.',
        },
        layout: 'simple' as const,
      },
    },
    {
      id: 'trend-report',
      title: 'The Trend Report and Reference Posts',
      description: 'Reference posts curated to match your brand positioning with social media insights.',
      cardConfig: {
        leftIcon: {
          show: true,
          icon: currentLeftIcon, // 根据流程步骤动态选择图标
          size: 'lg' as const,
        },
        rightIcon: {
          show: true,
          icon: 'card-right-icon2', // 使用自定义 SVG 图标
          size: 'md' as const,
        },
        content: {
          title: 'The Trend Report and Reference Posts',
          description: 'Reference posts curated to match your brand positioning.',
        },
        socialPost: {
          image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
          description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
          author: {
            name: 'Milla',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face',
          },
          stats: {
            likes: 30.1,
          },
        },
        button: {
          text: 'Select my preference',
          variant: 'primary' as const,
          position: 'bottom' as const,
        },
        layout: 'social' as const,
      },
    },
    {
      id: 'post-plan',
      title: 'RedNote Post Launch Plan',
      description: 'Strategic content planning for RedNote platform with engagement optimization.',
      cardConfig: {
        leftIcon: {
          show: true,
          icon: currentLeftIcon, // 根据流程步骤动态选择图标
          size: 'lg' as const,
        },
        rightIcon: {
          show: true,
          icon: 'card-right-icon3', // 使用自定义 SVG 图标
          size: 'md' as const,
        },
        content: {
          title: 'RedNote Post Launch Plan',
          description: 'Strategic content planning for RedNote platform with engagement optimization.',
        },
        socialPost: {
          image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
          description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
          author: {
            name: 'Milla',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face',
          },
          stats: {
            likes: 30.1,
          },
        },
        layout: 'social' as const,
      },
    },
    {
      id: 'trend-analysis',
      title: 'The Trend Report',
      description: 'Review the trend report based on your choice with detailed market insights.',
      cardConfig: {
        leftIcon: {
          show: true,
          icon: currentLeftIcon, // 根据流程步骤动态选择图标
          size: 'lg' as const,
        },
        rightIcon: {
          show: true,
          icon: 'card-right-icon4', // 使用自定义 SVG 图标
          size: 'md' as const,
        },
        content: {
          title: 'The Trend Report',
          description: 'Review the trend report based on your choice',
          customContent: 'TrendTopicBox',
        },
        button: {
          text: 'Select my preference',
          variant: 'primary' as const,
          position: 'bottom' as const,
        },
        layout: 'report' as const,
      },
    },
    {
      id: 'strategy-result',
      title: 'Result Card for Text Based Report',
      description: 'Comprehensive strategy results with actionable recommendations for implementation.',
      cardConfig: {
        leftIcon: {
          show: true,
          icon: currentLeftIcon, // 根据流程步骤动态选择图标
          size: 'lg' as const,
        },
        rightIcon: {
          show: true,
          icon: 'rightIcon', // 使用 rightIcon.svg
          size: 'md' as const,
        },
        content: {
          title: 'Result Card for Text Based Report',
          description: 'Comprehensive strategy results with actionable recommendations for implementation.',
        },
        button: {
          text: 'Approve Strategy',
          variant: 'primary' as const,
          position: 'bottom-left' as const,
        },
        layout: 'simple' as const,
      },
    },
  ]

  /** 为每张策略卡片创建消息 */
  if (import.meta.env.DEV) {
    /** 开发环境：批量创建，无延迟 */
    strategyCards.forEach((card) => {
      const cardClick = () => {
        taskStore.currentStep = step
        stateStore.isReportOpen = true
        /** 如果有对应的 reportItem，则打开它；否则显示默认内容 */
        if (reportItems.length > 0) {
          eventBus.emit(DistributionEvent.SetActiveTab, reportItems[0].id)
        }
      }

      createCardMessage(
        {
          title: card.title,
          description: card.description,
          variant: 'success',
          onClick: cardClick,
          /** 添加新的 cardConfig 属性 */
          cardConfig: card.cardConfig,
        },
        {
          meta: {
            step,
            cardId: card.id,
          },
        },
        store,
      )
    })
  }
  else {
    /** 生产环境：保持原有逻辑，可以添加适当延迟以提升用户体验 */
    strategyCards.forEach((card, index) => {
      setTimeout(() => {
        const cardClick = () => {
          taskStore.currentStep = step
          stateStore.isReportOpen = true
          /** 如果有对应的 reportItem，则打开它；否则显示默认内容 */
          if (reportItems.length > 0) {
            eventBus.emit(DistributionEvent.SetActiveTab, reportItems[0].id)
          }
        }

        createCardMessage(
          {
            title: card.title,
            description: card.description,
            variant: 'success',
            onClick: cardClick,
            /** 添加新的 cardConfig 属性 */
            cardConfig: card.cardConfig,
          },
          {
            meta: {
              step,
              cardId: card.id,
            },
          },
          store,
        )
      }, index * 200) // 生产环境每张卡片间隔200ms
    })
  }
}
