import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DaRenList } from '@/api/marketTestData/NewType'

import type { ButtonVariant } from '@/components/Button'
import type { CardConfig } from '@/components/Card/SelectableGradientCard'
import type { CodeMirrorLanguage } from '@/components/CodeMirrorEditor'
import type { ReactNode } from 'react'

/** 定义 Trend 模块的类型 */
export type StepNum = 'step1' | 'step2' | 'step3' | 'step4' | 'step5' | 'step6' | 'step7' | 'step8'

export type BiJiData = {
  /** 根据实际需求定义 */
  [key: string]: any
}

export type DaRenData = {
  /** 根据实际需求定义 */
  [key: string]: any
}

export type MessageType = 'text' | 'markdown' | 'loading' | 'thinking-start' | 'thinking-end' | 'card' | 'code' | 'biji_data' | 'biji_list' | 'daren_data' | 'daren_list' | 'phone_preview'

export type ChatMessage = {

  id: string

  sender: 'user' | 'assistant'

  type: MessageType

  content: string

  timestamp: number

  images?: {
    url: string
    caption?: string
  }[]

  files?: {
    name: string
    size: number
    url: string
    type: string
  }[]

  card?: {

    title?: string

    description?: string

    content?: ReactNode

    onClick?: () => void

    actions?: {
      label: string
      onClick: () => void
      type?: ButtonVariant
      disabled?: boolean
    }[]

    variant?: 'default' | 'info' | 'success' | 'warning' | 'error'

    bordered?: boolean

    icon?: ReactNode

    /** 新的卡片配置，用于支持重构后的 SelectableGradientCard */
    cardConfig?: CardConfig

    /** 自定义卡片样式类名 */
    cardClassName?: string
  }

  meta: {
    reportTitle?: string

    language?: CodeMirrorLanguage
    step: StepNum

    [key: string]: any
  }

  biji_data?: BiJiData[]

  daren_data?: DaRenData[]

  biji_list?: BiJiList

  daren_list?: DaRenList

  biji_title?: string

  title?: string
}

export type AgentStep = {

  id: string

  type: 'thinking' | 'planning' | 'executing' | 'result'

  title: string

  content: string

  status: 'pending' | 'running' | 'completed' | 'error'

  expanded?: boolean

  children?: AgentStep[]
}

export type Agent = {

  id: string

  name: string

  description: string

  icon: string

  steps: AgentStep[]

  active: boolean
}

export type ReportContentItem = {

  id: string

  type: 'text' | 'markdown' | 'image' | 'video' | 'file' | 'code' | 'biji_data' | 'biji_list' | 'daren_list' | 'phone_preview'

  title?: string

  content: string

  meta: {

    size?: number
    canTransformCode?: boolean
    isTransformDone?: boolean
    step: StepNum
    /**
     * 文件类型
     */
    mimeType?: string

    bijiData?: BiJiData[]

    bijiList?: BiJiList

    darenList?: DaRenList

    dimensions?: {
      width: number
      height: number
    }

    duration?: number

    thumbnail?: string

    description?: string

    language?: string

    codePreview?: {

      draggable?: boolean

      showControls?: boolean

      overflow?: 'hidden' | 'auto' | 'scroll' | 'visible'

      initialPosition?: {
        x?: number
        y?: number
      }
    }

    phonePreview?: {

      imageUrl?: string[]

      author?: string

      authorAvatar?: string

      likes?: number

      shares?: number

      comments?: number

      scale?: number
    }
  }
}

export type TaskStatus = 'complete' | 'in-progress' | 'waiting' | 'error' | 'cancelled'

export type TaskAction = {

  label: string

  type?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'default'

  loading?: boolean

  disabled?: boolean
}

export type AgentTask = {

  id: string

  title: string

  status: TaskStatus

  description?: string

  avatar?: string

  actions?: TaskAction[]

  progress?: number

  step: StepNum

  meta?: Record<string, any>

  collapsible?: boolean

  defaultCollapsed?: boolean
}

export type ReportCodeMap = {
  code: string
  done: boolean
  isToCoding: boolean
  mdIsProcessing: boolean
}
