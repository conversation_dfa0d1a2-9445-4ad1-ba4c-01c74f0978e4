import { lazy } from 'react'

export const HomeV3 = lazy(() => import('@/refactor/HomeV3'))
export const ZhHome = lazy(() => import('@/components/LoginZh'))

export const ErrorPage = lazy(() => import('@/pages/Error'))
export const NotFoundPage = lazy(() => import('@/pages/404'))

/** 登录 模块 */
export const Login = lazy(() => import('@/components/Login'))
/** 充值 模块 */
export const Recharge = lazy(() => import('@/pages/Recharge'))
export const Pricing = lazy(() => import('@/refactor/HomeV3/page/pricing'))
export const InnerPricing = lazy(() => import('@/refactor/HomeV3/page/innerPricing'))

// models 模块
export const Models = lazy(() => import('@/refactor/Models'))

// Photoe 模块
export const Photoe = lazy(() => import('@/pages/Photoe'))
export const Photog = lazy(() => import('@/pages/Photog'))
// export const Photog = lazy(() => import('@/pages/PhotogV3'))
export const MyLife = lazy(() => import('@/pages/MyLife'))

export const Video = lazy(() => import('@/pages/Video'))

export const History = lazy(() => import('@/pages/History'))
export const Assets = lazy(() => import('@/pages/Assets'))

export const Ask = lazy(() => import('@/pages/Ask'))
export const ChatV2 = lazy(() => import('@/pages/ChatV2'))
export const ChatV3 = lazy(() => import('@/pages/ChatV3'))
export const Agent = lazy(() => import('@/pages/Agent'))
export const Lab = lazy(() => import('@/pages/Lab'))

export const Distribution = lazy(() => import('@/pages/Distribution'))
export const Trend = lazy(() => import('@/pages/Trend'))
export const SelectableCardDebug = lazy(() => import('@/pages/SelectableCardDebug'))

export const Event = lazy(() => import('@/refactor/HomeV3/page/event'))
export const Blog = lazy(() => import('@/refactor/HomeV3/page/blog'))
export const BlogEditor = lazy(() => import('@/refactor/HomeV3/page/blog-editor'))

export const About = lazy(() => import('@/refactor/HomeV3/page/about'))
export const Careers = lazy(() => import('@/refactor/HomeV3/page/careers'))
export const News = lazy(() => import('@/refactor/HomeV3/page/news'))
